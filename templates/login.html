{% extends "base.html" %}

{% block title %}Connexion - Portail PDF Étudiant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='login.css') }}">
{% endblock %}

{% block content %}
<div class="login-page-wrapper">
    <div class="login-container">
        <p>Connectez-vous à votre compte pour continuer</p>

        <form method="POST" autocomplete="on">
            <div class="form-group">
                <label for="email">Adresse Email</label>
                <input type="email" id="email" name="email" placeholder="Entrez votre email" required autocomplete="username" maxlength="100">
            </div>

            <div class="form-group">
                <label for="password">Mot de Passe</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" placeholder="Entrez votre mot de passe" required autocomplete="current-password" maxlength="200" minlength="8">
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        👁️
                    </button>
                </div>
            </div>

            <button type="submit">Se Connecter</button>
        </form>

        <div class="signup-link">
            Vous n'avez pas de compte ? <a href="{{ url_for('signup') }}">S'inscrire</a>
        </div>

        <div class="oauth" style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
            <a href="{{ url_for('google_login') }}" style="display:inline-block;padding:10px 14px;border:1px solid #ccc;border-radius:6px;text-decoration:none;color:#111;background:#fff">
                🔐 Continuer avec Google
            </a>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;

    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
</script>
{% endblock %}
