{% extends "base.html" %}

{% block title %}Administration{% endblock %}

{% block content %}
<div class="section" style="max-width:800px;margin:30px auto;padding:24px;background:#fff;border-radius:12px;box-shadow:0 4px 12px rgba(0,0,0,.08)">
  <h2 style="margin:0 0 16px 0;">🔐 Administration</h2>
  <p style="color:#6b7280;margin-bottom:20px">Accès réservé. Vous êtes connecté en tant que <strong>{{ current_user.email }}</strong>.</p>

  <div style="display:grid;grid-template-columns:repeat(auto-fit,minmax(180px,1fr));gap:16px">
    <div style="background:#f8fafc;padding:16px;border-radius:10px;border:1px solid #e5e7eb">
      <div style="font-size:12px;color:#6b7280;margin-bottom:6px">Utilisateurs</div>
      <div style="font-size:26px;font-weight:700;color:#111827">{{ counts.utilisateurs }}</div>
    </div>
    <div style="background:#f8fafc;padding:16px;border-radius:10px;border:1px solid #e5e7eb">
      <div style="font-size:12px;color:#6b7280;margin-bottom:6px">Enseignants</div>
      <div style="font-size:26px;font-weight:700;color:#111827">{{ counts.enseignants }}</div>
    </div>
    <div style="background:#f8fafc;padding:16px;border-radius:10px;border:1px solid #e5e7eb">
      <div style="font-size:12px;color:#6b7280;margin-bottom:6px">Catégories</div>
      <div style="font-size:26px;font-weight:700;color:#111827">{{ counts.categories }}</div>
    </div>
    <div style="background:#f8fafc;padding:16px;border-radius:10px;border:1px solid #e5e7eb">
      <div style="font-size:12px;color:#6b7280;margin-bottom:6px">Classes</div>
      <div style="font-size:26px;font-weight:700;color:#111827">{{ counts.classes }}</div>
    </div>
    <div style="background:#f8fafc;padding:16px;border-radius:10px;border:1px solid #e5e7eb">
      <div style="font-size:12px;color:#6b7280;margin-bottom:6px">PDFs</div>
      <div style="font-size:26px;font-weight:700;color:#111827">{{ counts.pdfs }}</div>
    </div>
  </div>
</div>
{% endblock %}

