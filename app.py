"""
Flask Application - Educational Platform
A professional Flask application for managing teachers, classes, and educational content.
"""
import os
from flask import Flask, render_template, request, redirect, url_for, flash
# from werkzeug.utils import secure_filename
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user

from authlib.integrations.flask_client import OAuth
from secrets import token_urlsafe

# Import our modules
from models import PDF, Category, SchoolClass, db, User, Teacher
from config import config
# from routes.admin import admin_bp



# Create Flask app
app = Flask(__name__)

# Load configuration
config_name = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[config_name])

# Create upload directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize extensions
db.init_app(app)
# OAuth (Google)
oauth = OAuth()
oauth.init_app(app)
oauth.register(
    name='google',
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_id=os.environ.get('GOOGLE_CLIENT_ID'),
    client_secret=os.environ.get('GOOGLE_CLIENT_SECRET'),
    client_kwargs={'scope': 'openid email profile'}
)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Minimal inline admin (no blueprint)

@login_manager.user_loader
def load_user(user_id):
    # Try to load as regular user first, then as teacher
    user = User.query.get(int(user_id))
    if user:
        return user
    return Teacher.query.get(int(user_id))

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('home'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        user = User.query.filter_by(email=email).first()

        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('home'))
        else:
            flash('Email ou mot de passe invalide.', 'error')

    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('home'))

    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not first_name or not last_name or not email or not password or not confirm_password:
            flash('Veuillez remplir tous les champs obligatoires.', 'error')
            return render_template('signup.html')

        if password != confirm_password:
            flash('Les mots de passe ne correspondent pas.', 'error')
            return render_template('signup.html')

        if User.query.filter_by(email=email).first():
            flash('Cet email existe déjà.', 'error')
            return render_template('signup.html')

        user = User(
            first_name=first_name,
            last_name=last_name,
            email=email
        )
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        login_user(user)
        flash('Compte créé avec succès!', 'success')
        return redirect(url_for('home'))

    return render_template('signup.html')


@app.route('/login/google')
def google_login():
    if current_user.is_authenticated:
        return redirect(url_for('home'))
    redirect_uri = url_for('google_auth', _external=True)
    return oauth.google.authorize_redirect(redirect_uri)


@app.route('/auth/google')
def google_auth():
    token = oauth.google.authorize_access_token()
    userinfo = token.get('userinfo')
    if not userinfo:
        # Fallback: fetch userinfo via userinfo_endpoint
        resp = oauth.google.get('userinfo')
        userinfo = resp.json() if resp else None
    if not userinfo:
        flash("Échec d'authentification Google.", 'error')
        return redirect(url_for('login'))

    email = userinfo.get('email')
    given_name = userinfo.get('given_name') or ''
    family_name = userinfo.get('family_name') or ''

    user = User.query.filter_by(email=email).first()
    if not user:
        # Create a new local user; set a random password to satisfy schema
        user = User(first_name=given_name or 'Utilisateur', last_name=family_name or 'Google', email=email)
        # Random password
        user.set_password(token_urlsafe(32))
        db.session.add(user)
        db.session.commit()

    login_user(user)
    return redirect(url_for('home'))

@app.route('/home')
@login_required
def home():
    return render_template('home.html', user=current_user)

@app.route('/logout', methods=['POST'])
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# Admin sécurisé simple (login + liste blanche d'emails)
@app.route('/admin')
@login_required
def admin_panel():
    allowed = os.environ.get('ADMIN_ALLOWED_EMAILS', '')
    allowed_set = {e.strip().lower() for e in allowed.split(',') if e.strip()}
    if not allowed_set or (current_user.email or '').lower() not in allowed_set:
        flash("Accès administrateur refusé.", 'error')
        return redirect(url_for('home'))

    counts = {
        'utilisateurs': User.query.count(),
        'enseignants': Teacher.query.count(),
        'categories': Category.query.count(),
        'classes': SchoolClass.query.count(),
        'pdfs': PDF.query.count(),
    }
    return render_template('admin.html', counts=counts)





if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
